# Environment Configuration Guide

This guide explains how to configure the application for different environments (Development, UAT, Production).

## Environment-Specific URLs

The application uses different BASE_URL configurations for each environment:

| Environment | BASE_URL |
|-------------|----------|
| **Development/Internal** | `https://core.effihr.com/internal` |
| **UAT/Pre-production** | `https://core-preprod.effihr.com/internal` |
| **Production** | `https://core-prod.effihr.com/internal` |

## Quick Setup

### 1. Choose Your Environment

Copy the appropriate environment file to `.env`:

```bash
# For Development
cp .env.development .env

# For UAT
cp .env.uat .env

# For Production  
cp .env.production .env
```

### 2. Update Environment-Specific Values

After copying, update the following values in your `.env` file:

#### For UAT Environment:
- `API_KEY`: Replace `YOUR_UAT_API_KEY_HERE` with the actual UAT API key
- `LOAD_JOB_API_KEY`: Replace `YOUR_UAT_LOAD_JOB_API_KEY_HERE` with UAT load job key

#### For Production Environment:
- `BASE_URL`: Already set to `https://core-prod.effihr.com/internal`
- `API_KEY`: Replace `YOUR_PRODUCTION_API_KEY_HERE` with production API key
- `GENAI_API_KEY`: Replace with production GenAI API key
- `DB_NAME`: Replace with production database name
- `VERTEX_AI_SA_API`: Replace with production service account JSON
- `LOAD_JOB_API_KEY`: Replace with production load job API key

## Environment Variables Reference

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `BASE_URL` | API endpoint for document retrieval | `https://core-preprod.effihr.com/internal` |
| `API_KEY` | Authentication key for document API | `W5QWdKsWD3OC2m1Xj9niRiFRc1XpYpXi` |
| `GENAI_API_KEY` | Google Generative AI API key | `AIzaSyB-...` |
| `LOAD_JOB_API_KEY` | API key for load job authentication | `0A2C8B43...` |
| `VERTEX_AI_SA_API` | Service account JSON for Vertex AI | `{"type":"service_account",...}` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ACCEPT_LANGUAGE` | Language preference for API requests | `en-IN,en-US;q=0.9,en;q=0.8` |
| `TEMPERATURE` | Model temperature parameter | `0.45` |
| `TOP_K` | Model top-k parameter | `40` |
| `TOP_P` | Model top-p parameter | `0.1` |

## Validation

The application will validate environment configuration on startup:

1. **Required Variables Check**: Ensures all required environment variables are present
2. **URL Logging**: Logs the current BASE_URL for verification
3. **API Key Validation**: Validates API keys during first request

### Startup Logs

You should see logs like this on startup:

```
INFO: Documents service initialized with BASE_URL: https://core-preprod.effihr.com/internal
INFO: Scheduler started. Documents will be loaded daily at 12:00 Asia/Kolkata time.
```

## Testing Different Environments

### Manual Testing

Test the document loading endpoint for each environment:

```bash
# Development
curl -X POST "http://localhost:8000/documents/load" \
  -H "X-LOAD-API-KEY: your_dev_load_job_api_key"

# UAT  
curl -X POST "http://localhost:8000/documents/load" \
  -H "X-LOAD-API-KEY: your_uat_load_job_api_key"

# Production
curl -X POST "http://localhost:8000/documents/load" \
  -H "X-LOAD-API-KEY: your_prod_load_job_api_key"
```

### Environment Verification Script

Create a simple verification script:

```python
import os
from dotenv import load_dotenv

load_dotenv()

print(f"Environment: {os.getenv('BASE_URL')}")
print(f"Database: {os.getenv('DB_NAME')}")
print(f"API Key: {os.getenv('API_KEY', 'Not set')[:8]}...")
```

## Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use different API keys** for each environment
3. **Rotate keys regularly** in production
4. **Use environment-specific service accounts** for Vertex AI
5. **Monitor API usage** across environments

## Deployment Checklist

Before deploying to a new environment:

- [ ] Copy appropriate `.env.{environment}` file to `.env`
- [ ] Update all placeholder values with actual credentials
- [ ] Verify BASE_URL points to correct environment
- [ ] Test document loading endpoint
- [ ] Verify scheduler starts successfully
- [ ] Check logs for any configuration errors
- [ ] Test a few API calls to ensure connectivity

## Troubleshooting

### Common Issues

1. **"Invalid or missing API key"**: Check `LOAD_JOB_API_KEY` matches environment
2. **"Missing BASE_URL in environment"**: Ensure BASE_URL is set in `.env`
3. **Connection errors**: Verify BASE_URL is accessible from your deployment environment
4. **Document loading fails**: Check `API_KEY` is valid for the target environment

### Debug Commands

```bash
# Check current environment configuration
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print('BASE_URL:', os.getenv('BASE_URL'))"

# Test API connectivity
curl -I "$(python -c "import os; from dotenv import load_dotenv; load_dotenv(); print(os.getenv('BASE_URL'))")/policy/all"
```
