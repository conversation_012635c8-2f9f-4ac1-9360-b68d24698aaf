#!/usr/bin/env python3
"""
Environment switching utility for effi-rag application.
This script helps you switch between different environment configurations.
"""

import os
import shutil
import sys
from pathlib import Path

def switch_environment(env_name):
    """Switch to the specified environment configuration."""
    
    # Define available environments
    environments = {
        'dev': '.env.development',
        'development': '.env.development',
        'uat': '.env.uat',
        'preprod': '.env.uat',
        'prod': '.env.production',
        'production': '.env.production'
    }
    
    if env_name.lower() not in environments:
        print(f"❌ Unknown environment: {env_name}")
        print(f"Available environments: {', '.join(set(environments.values()))}")
        return False
    
    source_file = environments[env_name.lower()]
    target_file = '.env'
    
    # Check if source environment file exists
    if not os.path.exists(source_file):
        print(f"❌ Environment file not found: {source_file}")
        return False
    
    # Backup current .env if it exists
    if os.path.exists(target_file):
        backup_file = f"{target_file}.backup"
        shutil.copy2(target_file, backup_file)
        print(f"📋 Backed up current .env to {backup_file}")
    
    # Copy environment file to .env
    shutil.copy2(source_file, target_file)
    print(f"✅ Switched to {env_name} environment")
    print(f"📁 Copied {source_file} → {target_file}")
    
    # Show current configuration
    show_current_config()
    
    return True

def show_current_config():
    """Display current environment configuration."""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        base_url = os.getenv('BASE_URL', 'Not set')
        db_name = os.getenv('DB_NAME', 'Not set')
        api_key = os.getenv('API_KEY', 'Not set')
        
        print("\n📊 Current Configuration:")
        print(f"   BASE_URL: {base_url}")
        print(f"   DB_NAME: {db_name}")
        print(f"   API_KEY: {api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else api_key}")
        
        # Determine environment type based on BASE_URL
        if 'core.effihr.com/internal' in base_url:
            env_type = "🔧 Development/Internal"
        elif 'core-preprod.effihr.com' in base_url:
            env_type = "🧪 UAT/Pre-production"
        elif 'core-prod.effihr.com' in base_url:
            env_type = "🚀 Production"
        else:
            env_type = "❓ Unknown"
            
        print(f"   Environment: {env_type}")
        
    except Exception as e:
        print(f"⚠️  Could not load configuration: {e}")

def list_environments():
    """List available environment files."""
    print("📋 Available Environment Files:")
    
    env_files = [
        ('.env.development', '🔧 Development/Internal'),
        ('.env.uat', '🧪 UAT/Pre-production'), 
        ('.env.production', '🚀 Production')
    ]
    
    for file_name, description in env_files:
        if os.path.exists(file_name):
            print(f"   ✅ {file_name} - {description}")
        else:
            print(f"   ❌ {file_name} - {description} (missing)")

def main():
    """Main function to handle command line arguments."""
    
    print("🔄 Environment Switcher for effi-rag")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage: python switch_environment.py <environment>")
        print("\nAvailable environments:")
        print("  dev, development  - Development/Internal environment")
        print("  uat, preprod      - UAT/Pre-production environment") 
        print("  prod, production  - Production environment")
        print("\nOther commands:")
        print("  list              - List available environment files")
        print("  current           - Show current configuration")
        print("\nExample:")
        print("  python switch_environment.py uat")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'list':
        list_environments()
    elif command == 'current':
        show_current_config()
    elif command in ['help', '-h', '--help']:
        main()
    else:
        switch_environment(command)

if __name__ == "__main__":
    main()
