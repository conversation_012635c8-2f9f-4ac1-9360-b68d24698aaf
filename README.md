 # effi-rag

A RAG (Retrieval-Augmented Generation) implementation for effiHR, integrating with Gemini AI.

## System Architecture

```mermaid
flowchart TD
    User([User]) --> |1. Sends query| API[FastAPI Server]

    subgraph Document Processing
        Documents[Documents Service] --> |2a. Fetch documents| S3[(S3 Storage)]
        S3 --> |2b. Return documents| Documents
        Documents --> |2c. Process documents| Embeddings[Generate Embeddings]
        Embeddings --> |2d. Store embeddings| VectorDB[(ChromaDB)]
    end

    subgraph Query Processing
        API --> |3. Process query| Chat[Chat Service]
        Chat --> |4. Retrieve relevant passages| VectorDB
        VectorDB --> |5. Return relevant passages| Chat
        Chat --> |6. Create prompt with context| GeminiAI[Gemini AI]
        GeminiAI --> |7. Generate response| Chat
        Chat --> |8. Stream response| API
    end

    API --> |9. Return response| User

    class User,API,Documents,S3,Embeddings,VectorDB,<PERSON><PERSON>,GeminiAI primary
```

## Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant Server as FastAPI Server
    participant Chat as Chat Service
    participant VectorDB as ChromaDB
    participant LLM as Gemini AI
    participant Docs as Documents Service
    participant S3 as S3 Storage

    Note over User,S3: Document Loading Flow
    User->>Server: Load documents request
    Server->>Docs: Get documents
    Docs->>S3: Download documents
    S3->>Docs: Return document content
    Docs->>Docs: Process documents (PDF/DOCX)
    Docs->>VectorDB: Store embeddings
    VectorDB->>Server: Confirm storage
    Server->>User: Documents loaded successfully

    Note over User,LLM: Query Processing Flow
    User->>Server: Send query with organisation_id and role_id
    Server->>Chat: Process query
    Chat->>VectorDB: Retrieve relevant passages
    VectorDB->>Chat: Return relevant context
    Chat->>Chat: Create prompt with context
    Chat->>LLM: Generate response with context
    LLM-->>Chat: Stream response tokens
    Chat-->>Server: Stream response
    Server-->>User: Stream response to user
```
