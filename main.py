import os
from contextlib import asynccontextmanager
from dotenv import load_dotenv
from fastapi import FastAPI
import yaml
from utils.logger import logger
from fastapi.middleware.cors import CORSMiddleware

from routers.chat import chat_router
from routers.documents import documents_router, load_documents_core
from services.vectordb import VectorDb
from services.geminiai import GeminiEmbeddingFunction
import asyncio
import hashlib
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
import pytz

load_dotenv()

DB_NAME = os.getenv("DB_NAME")
if not DB_NAME:
    raise RuntimeError("Missing required environment variable: DB_NAME")

COMMON_QUERIES_FILE = "config/common_queries.yaml"

@asynccontextmanager
async def lifespan(app: FastAPI):
    gemini_embedding_function = GeminiEmbeddingFunction()
    vector_storage = VectorDb(
        database_name=DB_NAME,  # type: ignore
        embedding_function=gemini_embedding_function,
        embedding_function_async=None,
    )  # type: ignore
    app.state.vector_storage = vector_storage

    # Load common queries from YAML
    try:
        with open(COMMON_QUERIES_FILE, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f) or {}
            common_queries = config.get('common_queries', []) or []
            logger.info(f"Loaded {len(common_queries)} common queries from {COMMON_QUERIES_FILE}.")
    except FileNotFoundError:
        logger.warning(f"Common queries file not found at {COMMON_QUERIES_FILE}. Skipping pre-embedding.")
        common_queries = []
    except yaml.YAMLError as e:
        logger.error(f"Error parsing common queries YAML {COMMON_QUERIES_FILE}: {e}. Skipping pre-embedding.")
        common_queries = []

    loop = asyncio.get_event_loop()
    for query_text in common_queries:
        qid = hashlib.sha256(query_text.encode('utf-8')).hexdigest()

        try:
            record = await loop.run_in_executor(
                None,
                lambda: vector_storage.query_store.get(ids=[qid], include=["embeddings"])
            ) or {}
            raw_embs = record.get("embeddings")
           
            if hasattr(raw_embs, "tolist"):
                raw_embs = raw_embs.tolist() # type: ignore
            embs = raw_embs or []

            
            if isinstance(embs, (list, tuple)) \
               and len(embs) > 0 \
               and isinstance(embs[0], (list, tuple)) \
               and len(embs[0]) > 0:
                logger.info(f"Embedding exists for '{query_text[:50]}...'; skipping persist.")
                continue
        except Exception as e:
            logger.warning(f"Error checking existing embedding for '{query_text[:50]}...': {e}")
            
        try:
           
            raw_result = await loop.run_in_executor(
                None,
                lambda: gemini_embedding_function([query_text])
            )
            result_list = raw_result[0]
            if hasattr(result_list, "tolist"):
                result_list = result_list.tolist()

            # explicit size check
            if not isinstance(result_list, (list, tuple)) or len(result_list) == 0:
                logger.error(f"Embedding service returned empty for '{query_text[:50]}...'; skipping.")
                continue

            emb_list = [float(v) for v in result_list]

            await loop.run_in_executor(
                None,
                lambda: vector_storage.query_store.add(
                    ids=[qid],
                    embeddings=[emb_list],
                    documents=[query_text]
                )
            )
            logger.info(f"Generated & persisted embedding for '{query_text[:50]}...' as id {qid}.")
        except Exception as e:
            logger.error(f"Failed to generate/persist embedding for '{query_text[:50]}...': {e}")

    # Set up the scheduler for daily document loading
    scheduler = AsyncIOScheduler()

    # Define the scheduled job function
    async def scheduled_load_documents():
        """Scheduled job to load documents daily at 12:00 Asia/Kolkata"""
        try:
            logger.info("Starting scheduled document loading job...")
            success = await load_documents_core(vector_storage)
            if success:
                logger.info("Scheduled document loading completed successfully.")
            else:
                logger.error("Scheduled document loading failed. Check logs for details.")
        except Exception as e:
            logger.error(f"Error in scheduled document loading: {e}")

    # Schedule the job to run daily at 12:00 Asia/Kolkata time
    asia_kolkata = pytz.timezone('Asia/Kolkata')
    scheduler.add_job(
        scheduled_load_documents,
        CronTrigger(hour=12, minute=0, timezone=asia_kolkata),
        id='daily_document_load',
        name='Daily Document Loading',
        replace_existing=True
    )

    # Start the scheduler
    scheduler.start()
    logger.info("Scheduler started. Documents will be loaded daily at 12:00 Asia/Kolkata time.")

    # Store scheduler in app state for potential access
    app.state.scheduler = scheduler

    yield

    # Shutdown the scheduler
    scheduler.shutdown()
    logger.info("Scheduler shutdown completed.")

app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods including OPTIONS
    allow_headers=["*"],  # Allows all headers
)

app.include_router(chat_router)
app.include_router(documents_router)

@app.get("/")
async def read_root():
    return {"message": "API is up and running. Visit /docs for Swagger UI."}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, timeout_keep_alive=300)
