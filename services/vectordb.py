import os
import time
import asyncio
import hashlib
from typing import Callable, List, Optional, Awaitable, Any, Dict, Union

from chromadb import PersistentClient, errors as chroma_errors
from chromadb.config import Settings
from chromadb import EmbeddingFunction
from chromadb.api.types import GetResult, Embeddings
from utils.logger import logger # Assuming logger is correctly configured

# Import numpy for type checking and conversion
import numpy as np

AsyncEmbeddingFunction = Callable[[List[str]], Awaitable[List[List[float]]]]
CHROMA_PERSIST_DIR = os.getenv("CHROMA_PERSIST_DIR", "./chroma_data")
os.makedirs(CHROMA_PERSIST_DIR, exist_ok=True)

# Define TTL in seconds (7 days)
CACHE_TTL_SECONDS = 7 * 24 * 60 * 60

class VectorDb:
    """
    A class to manage interactions with ChromaDB, including creating,
    deleting, and querying vector collections. It supports both synchronous
    and asynchronous embedding functions and includes query caching with TTL.
    """

    def __init__(
        self,
        organisation_id: str = "default_organisation",
        database_name: str = "default_database",
        # Use Optional for embedding_function as it might not be provided if async is used
        embedding_function: Optional[EmbeddingFunction] = None,
        embedding_function_async: Optional[AsyncEmbeddingFunction] = None,
    ):
        """
        Initializes the VectorDb instance, setting up the ChromaDB client
        and the query store.

        Args:
            organisation_id (str): Identifier for the organisation.
            database_name (str): Name of the database.
            embedding_function (Optional[EmbeddingFunction]): Synchronous embedding function.
            embedding_function_async (Optional[AsyncEmbeddingFunction]): Asynchronous embedding function.
        """
        self.organisation_id = organisation_id
        self.database_name = database_name
        self.embedding_function = embedding_function
        self.embedding_function_async = embedding_function_async

        # Ensure at least one embedding function is provided
        if not (self.embedding_function or self.embedding_function_async):
            raise ValueError("Provide at least one embedding_function (sync or async) to VectorDb.")

        # Initialize the ChromaDB persistent client
        self.chroma_client = PersistentClient(
            path=CHROMA_PERSIST_DIR,
            settings=Settings(),
        )

        # Initialize query_store with embedding_function if available for consistency
        if self.embedding_function:
            self.query_store = self.chroma_client.get_or_create_collection(
                name=f"{self.organisation_id}_{self.database_name}_query_store",
                embedding_function=self.embedding_function # Recommended to set if available
            )
            logger.info(
                f"Initialized Chroma client for organisation '{self.organisation_id}', "
                f"database '{self.database_name}'. Query store: '{self.query_store.name}'"
            )
        else:
            # Note: If no embedding_function is provided here, you MUST provide embeddings when adding/updating
            #       items to query_store if you want to use its query capabilities.
            self.query_store = self.chroma_client.get_or_create_collection(
                name=f"{self.organisation_id}_{self.database_name}_query_store"
            )
            logger.warning(
                f"Initialized Chroma client for organisation '{self.organisation_id}', "
                f"database '{self.database_name}'. Query store '{self.query_store.name}' "
                "without an embedding_function. Embeddings must be provided manually for upserts."
            )
            
        # Schedule the background cleanup task
        # This will run a separate async task to clean up old cache entries periodically.
        # Ensure your application's event loop is running (e.g., if using FastAPI/Uvicorn).
        asyncio.create_task(self._start_background_cache_cleanup())

    # --- NEW METHODS FOR CACHE TTL AND CLEANUP ---
    async def _start_background_cache_cleanup(self, interval_seconds: int = 24 * 60 * 60): # Run once every 24 hours
        """
        Starts a background task to periodically clean up expired cache entries.
        """
        while True:
            logger.info(f"Starting background cache cleanup for '{self.query_store.name}'...")
            await self._cleanup_expired_cache_entries()
            logger.info(f"Background cache cleanup completed. Next run in {interval_seconds / 3600:.1f} hours.")
            await asyncio.sleep(interval_seconds)

    async def _cleanup_expired_cache_entries(self):
        """
        Deletes cache entries from query_store that have expired based on CACHE_TTL_SECONDS.
        This is typically run as a background task.
        """
        loop = asyncio.get_event_loop()
        expired_ids = []
        offset = 0
        limit = 1000 # Fetch IDs in batches

        current_time = time.time()
        
        while True:
            try:
                # Fetch IDs and metadatas in batches
                batch_result: GetResult = await loop.run_in_executor(
                    None,
                    lambda: self.query_store.get(
                        offset=offset,
                        limit=limit,
                        include=["metadatas"] # Only need metadatas to check timestamp
                    )
                )
                
                batch_ids = batch_result.get('ids', [])
                batch_metadatas = batch_result.get('metadatas', [])

                if not batch_ids:
                    break # No more entries

                for i, metadata in enumerate(batch_metadatas): # type: ignore
                    # Check if metadata is a dict and contains 'timestamp'
                    if metadata and isinstance(metadata, dict) and "timestamp" in metadata:
                        stored_timestamp = metadata["timestamp"]
                        if (current_time - stored_timestamp) > CACHE_TTL_SECONDS: # type: ignore
                            expired_ids.append(batch_ids[i])
                    else:
                        # Log and add entries without timestamp to be cleaned up
                        logger.warning(f"Cache entry ID '{batch_ids[i]}' missing 'timestamp' metadata or malformed. Considering for deletion.")
                        expired_ids.append(batch_ids[i]) 

                offset += limit
                
            except Exception as e:
                logger.error(f"Error fetching cache entries for cleanup: {e}")
                break # Exit loop on error

        if expired_ids:
            # Delete expired entries in batches to avoid large single operations
            batch_size = 500
            for i in range(0, len(expired_ids), batch_size):
                ids_to_delete = expired_ids[i : i + batch_size]
                try:
                    await loop.run_in_executor(
                        None,
                        lambda ids=ids_to_delete: self.query_store.delete(ids=ids)
                    )
                    logger.info(f"Cleaned up {len(ids_to_delete)} expired cache entries.")
                except Exception as e:
                    logger.error(f"Error deleting batch of expired cache entries: {e}")
        else:
            logger.info("No expired cache entries found during background cleanup.")

    # --- END NEW METHODS ---

    async def create_chroma_db(self, documents: List[str], collection_key: str) -> Any:
        """
        Creates or updates a ChromaDB collection with the given documents.
        It purges existing documents in the collection and adds new ones.

        Args:
            documents (List[str]): A list of text documents to be added.
            collection_key (str): The name of the collection.

        Returns:
            Tuple[Dict, Optional[Dict]]: A tuple containing a success message
                                         and None, or None and an error message.
        """
        loop = asyncio.get_event_loop()
        try:
            # Directly use embedding_function when creating/getting the collection if it exists
            collection = await loop.run_in_executor(
                None,
                lambda: self.chroma_client.get_or_create_collection(
                    name=collection_key,
                    embedding_function=self.embedding_function # Pass embedding function here
                )
            )
            logger.info(f"Accessed/Created collection '{collection.name}'.")

            # Efficiently get and delete existing IDs
            existing_ids = []
            offset, limit = 0, 1000
            while True:
                # Use collection.peek() or collection.get with specific filters if possible
                # However, for purging all, iterating is common.
                batch_result = await loop.run_in_executor(
                    None,
                    lambda off=offset, lim=limit: collection.get(offset=off, limit=lim, include=[]) # Only need IDs
                )
                batch_ids = batch_result.get('ids', []) if batch_result else []
                if not batch_ids:
                    break
                existing_ids.extend(batch_ids)
                offset += limit
            logger.debug(f"Retrieved {len(existing_ids)} existing IDs for '{collection_key}'.")

            if existing_ids:
                # Batch delete
                await loop.run_in_executor(
                    None,
                    lambda ids=existing_ids: collection.delete(ids=ids)
                )
                logger.info(f"Purged {len(existing_ids)} old docs from '{collection.name}'.")

            unique_docs_map = {}
            for doc in documents:
                doc_hash = hashlib.sha256(doc.encode()).hexdigest()
                doc_id = f"{collection_key}_doc_{doc_hash}"
                if doc_id not in unique_docs_map:
                    unique_docs_map[doc_id] = doc

            new_docs = list(unique_docs_map.values())
            new_ids = list(unique_docs_map.keys())
            new_metadatas: List[Dict[str, Any]] = []
            for doc in new_docs:
                policy_title = "Unknown Policy"
                lines = [line.strip() for line in doc.split('\n') if line.strip()]
                if lines:
                    potential_title = " ".join(lines[0].split())
                    if len(potential_title) > 50:
                        potential_title = potential_title[:50] + "..."
                    policy_title = potential_title
                new_metadatas.append({"policy_title": policy_title})

            if new_docs:
                batch_size = 100 # Batch size for adding documents
                for i in range(0, len(new_docs), batch_size):
                    sub_docs = new_docs[i : i + batch_size]
                    sub_ids = new_ids[i : i + batch_size]
                    sub_metadatas = new_metadatas[i : i + batch_size]

                    await loop.run_in_executor(
                        None,
                        lambda sd=sub_docs, si=sub_ids, sm=sub_metadatas: collection.add(
                            documents=sd, ids=si, metadatas=sm # type: ignore
                        )
                    )
                logger.info(f"Added {len(new_docs)} docs to '{collection.name}'.")
                logger.info(f"Chroma reloaded for '{collection.name}'")
            else:
                logger.info(f"No new docs to add to '{collection.name}'.")

            return {"message": f"Collection '{collection.name}' updated."}, None

        except Exception as e:
            logger.error(f"Error in create_chroma_db for collection '{collection_key}': {e}")
            return None, {"message": f"Error creating Chroma DB: {e}"}

    async def get_chroma_db(self, collection_id: str) -> Optional[Any]:
        """
        Fetches an existing ChromaDB collection by its name.

        Args:
            collection_id (str): The name of the collection to fetch.

        Returns:
            Optional[Any]: The ChromaDB collection object if found, otherwise None.
        """
        loop = asyncio.get_event_loop()
        try:
            db = await loop.run_in_executor(
                None,
                lambda: self.chroma_client.get_collection(
                    name=collection_id,
                    embedding_function=self.embedding_function # Pass embedding function here
                )
            )
            return db
        except chroma_errors.NotFoundError:
            logger.error(f"Collection '{collection_id}' not found.")
            return None
        except Exception as e:
            logger.error(f"Error getting collection '{collection_id}': {e}")
            return None

    async def delete_chroma_db(self, collection_id: str) -> Any:
        """
        Deletes a ChromaDB collection by its name.

        Args:
            collection_id (str): The name of the collection to delete.

        Returns:
            Tuple[Dict, Optional[Dict]]: A tuple containing a success message
                                         and None, or None and an error message.
        """
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(
                None,
                lambda: self.chroma_client.delete_collection(name=collection_id)
            )
            return {"message": f"Collection '{collection_id}' deleted."}, None
        except chroma_errors.NotFoundError:
            logger.error(f"Collection '{collection_id}' not found for deletion.")
            return None, {"message": f"Collection '{collection_id}' not found."}
        except Exception as e:
            logger.error(f"Error deleting collection '{collection_id}': {e}")
            return None, {"message": f"Error deleting: {e}"}

    async def get_relevant_passage_from_db(self, query: str, db) -> Optional[str]:
        """
        Retrieves the most relevant document passage from the specified ChromaDB collection
        based on the query. It uses a cached embedding for the query if available,
        otherwise generates and persists it. Includes TTL logic for cached entries.

        Args:
            query (str): The user's query string.
            db (Any): The ChromaDB collection object to query against.

        Returns:
            Optional[str]: A concatenated string of all relevant document passages
                           found within the threshold, or None if no passages are found.
        """
        total_start_time = time.perf_counter() # Start total timer

        if db is None:
            logger.warning("Cannot query: DB is None.")
            logger.info(f"[total] {(time.perf_counter() - total_start_time)*1000:.1f}ms")
            return None

        query_id = hashlib.sha256(query.encode()).hexdigest()
        loop = asyncio.get_event_loop()
        embedding: Optional[List[float]] = None
        cached_answer: Optional[str] = None
        
        # --- Cache Retrieval with TTL Check ---
        cache_get_start = time.perf_counter()
        try:
            record_result: Optional[GetResult] = await loop.run_in_executor(
                None,
                lambda: self.query_store.get(ids=[query_id], include=["embeddings", "metadatas"])
            )
            cache_get_end = time.perf_counter()
            logger.debug(f"[cache_get] {(cache_get_end - cache_get_start)*1000:.1f}ms for '{query_id}'")

            # Correctly handle potential empty list/None for embeddings and metadata
            raw_embeddings_from_get: Optional[Embeddings] = record_result.get("embeddings") if record_result else None # type: ignore
            record_metadatas: Optional[List[Dict[str, Any]]] = record_result.get("metadatas") if record_result else None # type: ignore

            # Convert to NumPy array for consistent size check
            embeddings_np = np.array(raw_embeddings_from_get) if raw_embeddings_from_get is not None else np.array([])

            # Check if embeddings_np is not empty and the first element is not None
            if embeddings_np.size > 0 and embeddings_np[0] is not None:
                first_embedding_item: Any = embeddings_np[0]
                
                # Handle potential numpy array or list/tuple conversion to ensure List[float]
                try:
                    if isinstance(first_embedding_item, np.ndarray):
                        embedding = first_embedding_item.tolist()
                        logger.debug(f"Converted cached embedding from numpy.ndarray to list for '{query[:50]}...'.")
                    elif isinstance(first_embedding_item, (list, tuple)):
                        embedding = [float(x) for x in first_embedding_item]
                        logger.debug(f"Converted cached embedding from list/tuple to list[float] for '{query[:50]}...'.")
                    else:
                        logger.warning(f"Stored embedding for '{query[:50]}...' is in an unexpected format: {type(first_embedding_item)}. Attempting direct conversion.")
                        embedding = list(map(float, first_embedding_item)) # Try converting any iterable
                except Exception as e:
                    logger.warning(f"Failed to convert stored embedding to list[float] for '{query[:50]}...': {e}")
                    embedding = None

                # Check TTL and cached answer (MODIFIED)
                if embedding and record_metadatas and len(record_metadatas) > 0 and record_metadatas[0]:
                    first_metadata_item = record_metadatas[0]
                    
                    if isinstance(first_metadata_item, dict) and "cached_passage" in first_metadata_item and "timestamp" in first_metadata_item:
                        stored_timestamp = first_metadata_item["timestamp"]
                        if (time.time() - stored_timestamp) < CACHE_TTL_SECONDS:
                            cached_answer = first_metadata_item["cached_passage"]
                            logger.info(f"Loaded cached answer for '{query[:50]}...' from store (TTL OK).")
                            
                            # Update timestamp to reset TTL on cache hit (LRU-like behavior)
                            await loop.run_in_executor(
                                None,
                                lambda: self.query_store.update(
                                    ids=[query_id],
                                    metadatas=[{"cached_passage": cached_answer, "timestamp": time.time()}]
                                )
                            )
                            logger.debug(f"Updated timestamp for '{query[:50]}' in cache.")
                            
                            logger.info(f"[total] {(time.perf_counter() - total_start_time)*1000:.1f}ms")
                            return cached_answer
                        else:
                            logger.info(f"Cached entry for '{query[:50]}...' expired. Deleting from cache.")
                            await loop.run_in_executor(
                                None,
                                lambda: self.query_store.delete(ids=[query_id])
                            )
                            embedding = None # Force re-embedding
                            cached_answer = None
                    else:
                        logger.warning(f"Cached entry for '{query[:50]}...' is missing 'cached_passage' or 'timestamp' metadata. Forcing re-embedding.")
                        # Force re-embedding and re-caching if metadata is incomplete/malformed
                        embedding = None 
                        cached_answer = None
                else:
                    logger.info(f"No valid stored embedding or complete metadata found for '{query[:50]}...'. Generating.")
                    embedding = None # Force re-embedding if no embedding or metadata present

            else:
                logger.info(f"No stored embedding found for '{query[:50]}...'. Generating.")
                embedding = None # Force re-embedding

        except Exception as e:
            cache_get_end = time.perf_counter()
            logger.warning(f"Error during attempt to load stored embedding/metadata for '{query[:50]}...': {e}")
            logger.debug(f"[cache_get_error] {(cache_get_end - cache_get_start)*1000:.1f}ms for '{query_id}'")
            embedding = None
            cached_answer = None

        # --- Embedding Generation (if not from cache or expired) ---
        if embedding is None:
            embed_gen_start = time.perf_counter()
            try:
                result_raw: Optional[List[List[float]]] = None
                if self.embedding_function_async:
                    result_raw = await self.embedding_function_async([query])
                elif self.embedding_function:
                    result_raw = await loop.run_in_executor(
                        None, self.embedding_function, [query]
                    ) # type: ignore
                else:
                    logger.error("No embedding function provided to generate embedding for query.")
                    logger.info(f"[total] {(time.perf_counter() - total_start_time)*1000:.1f}ms")
                    return None

                # Convert to NumPy array for consistent size check
                generated_embeddings_np = np.array(result_raw) if result_raw is not None else np.array([])

                if generated_embeddings_np.size > 0 and generated_embeddings_np[0] is not None:
                    first_gen_embedding_item: Any = generated_embeddings_np[0]
                    # Convert NumPy array to list if necessary
                    try:
                        if isinstance(first_gen_embedding_item, np.ndarray):
                            embedding = first_gen_embedding_item.tolist()
                            logger.debug(f"Converted generated embedding from numpy.ndarray to list for '{query[:50]}...'.")
                        elif isinstance(first_gen_embedding_item, (list, tuple)):
                            embedding = [float(x) for x in first_gen_embedding_item]
                        else:
                            logger.error(f"Generated embedding for '{query}' is in an unexpected format: {type(first_gen_embedding_item)}. Result: {result_raw}. Attempting direct conversion.")
                            embedding = list(map(float, first_gen_embedding_item)) # Try converting any iterable
                    except Exception as e:
                        logger.error(f"Failed to convert generated embedding to list[float] for '{query}'. Result: {result_raw}. Error: {e}")
                        embedding = None
                else:
                    logger.error(f"Generated an empty or malformed embedding for '{query}'. Result: {result_raw}")
                    embedding = None

                if not embedding:
                    logger.info(f"[embed] {(time.perf_counter() - embed_gen_start)*1000:.1f}ms")
                    logger.info(f"[total] {(time.perf_counter() - total_start_time)*1000:.1f}ms")
                    return None

                logger.info(f"[embed] {(time.perf_counter() - embed_gen_start)*1000:.1f}ms")

            except Exception as e:
                embed_gen_end = time.perf_counter()
                logger.error(f"Failed to generate embedding for '{query[:50]}...': {e}")
                logger.info(f"[embed_error] {(embed_gen_end - embed_gen_start)*1000:.1f}ms")
                logger.info(f"[total] {(time.perf_counter() - total_start_time)*1000:.1f}ms")
                return None

        if embedding is None:
            logger.error("Embedding is None after generation attempt. Cannot proceed with query.")
            logger.info(f"[total] {(time.perf_counter() - total_start_time)*1000:.1f}ms")
            return None

        # --- ChromaDB Query ---
        K = 5 # Number of top results to retrieve from ChromaDB
        DIST_THRESHOLD = 1 # Keep this as 1 for now, assuming L2 or Cosine distance transformed to 0-2 range.

        relevant_passages = []
        chroma_query_start = time.perf_counter()
        try:
            include = ["documents", "distances", "metadatas"]

            res = await loop.run_in_executor(
                None,
                lambda: db.query(
                    query_embeddings=[embedding],
                    n_results=K,
                    include=include
                )
            )
            chroma_query_end = time.perf_counter()
            logger.info(f"[chroma] {(chroma_query_end - chroma_query_start)*1000:.1f}ms")

            # Defensive unpacking of results
            ids = res.get("ids", [[]])[0]
            docs = res.get("documents", [[]])[0]
            dists = res.get("distances", [[]])[0]
            metadatas = res.get("metadatas", [[]])[0]

            if dists and isinstance(dists, list) and dists:
                logger.debug(f"Top-{K} distances for '{query[:50]}...': {dists}")
            else:
                logger.debug(f"No distances returned for query '{query[:50]}...'.")

            min_len = min(len(docs), len(dists), len(metadatas), len(ids))
            for i in range(min_len):
                doc = docs[i]
                dist = dists[i]
                metadata = metadatas[i]

                if dist < DIST_THRESHOLD:
                    policy_name = None
                    if isinstance(metadata, dict):
                        policy_name = metadata.get("policy_title")

                    if policy_name:
                        log_message = f"Found relevant passage (Policy: '{policy_name}') with distance {dist:.3f}."
                    else:
                        current_doc_id = ids[i] if i < len(ids) else "N/A"
                        truncated_doc = doc[:50].replace('\n', ' ') + '...' if len(doc) > 50 else doc.replace('\n', ' ')
                        log_message = f"Found relevant passage (ID: {current_doc_id}, snippet: '{truncated_doc}') with distance {dist:.3f}."
                    logger.info(log_message)
                    relevant_passages.append(doc)

            if relevant_passages:
                combined_passage = "\n\n---\n\n".join(relevant_passages)
                # --- Caching the result with current timestamp ---
                cache_upsert_start = time.perf_counter()
                metadata = {"cached_passage": combined_passage, "timestamp": time.time()} # ADDED TIMESTAMP
                await loop.run_in_executor(
                    None,
                    lambda: self.query_store.upsert(
                        ids=[query_id],
                        embeddings=[embedding],
                        documents=[query], # Store the original query text
                        metadatas=[metadata]
                    )
                )
                cache_upsert_end = time.perf_counter()
                logger.info(f"[cache_upsert] {(cache_upsert_end - cache_upsert_start)*1000:.1f}ms")
                logger.info(f"Persisted new embedding and relevant passage for '{query[:50]}...' as id {query_id}.")
                
                total_end_time = time.perf_counter()
                logger.info(f"[total] {(total_end_time - total_start_time)*1000:.1f}ms")
                logger.info(f"Relevant passage retrieved. Length: {len(combined_passage)} characters.")
                return combined_passage
            else:
                logger.info(f"No relevant passage found within DIST_THRESHOLD={DIST_THRESHOLD} for '{query[:50]}...'.")
                total_end_time = time.perf_counter()
                logger.info(f"[total] {(total_end_time - total_start_time)*1000:.1f}ms")
                return None

        except Exception as e:
            chroma_query_end = time.perf_counter() # Ensure end time is marked even on error
            logger.error(f"Query retrieval error from ChromaDB for '{query[:50]}...': {e}")
            logger.info(f"[chroma_query_error] {(chroma_query_end - chroma_query_start)*1000:.1f}ms")
            total_end_time = time.perf_counter()
            logger.info(f"[total] {(total_end_time - total_start_time)*1000:.1f}ms")
            return None
