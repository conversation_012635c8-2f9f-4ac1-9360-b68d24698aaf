from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware


class ServiceClient:
    def __init__(self, lifespan=None):
        self.app = FastAPI(title="effiHR", version="1.0.0", lifespan=lifespan)
        self.initialize_middlewares()

    def initialize_middlewares(self):
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # ← allow everything (only for development!)
            allow_methods="*",
            allow_headers=["*"],
            allow_credentials=True,
            expose_headers=["*"],
        )
