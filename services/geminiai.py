import os
import json
from typing import Generator, List
from dotenv import load_dotenv
import chromadb
from chromadb import Documents, EmbeddingFunction
from chromadb.api.types import Embeddings
from google.oauth2.service_account import Credentials
from google import genai
from google.genai import types
from utils.logger import logger
import vertexai
from vertexai.language_models import TextEmbeddingModel

load_dotenv()

MODEL_ID = os.getenv("MODEL_ID", "gemini-2.5-flash-preview-04-17")
EMBEDDING_MODEL_ID = os.getenv("EMBEDDING_MODEL_ID", "gemini-embedding-001")
MAX_CACHE_SIZE = int(os.getenv("MAX_CACHE_SIZE", "1024"))
TEMPERATURE = float(os.getenv("TEMPERATURE", "0.45"))
TOP_P = float(os.getenv("TOP_P", "0.1"))
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID", "")
GCP_LOCATION = os.getenv("GCP_LOCATION", "asia-south1")
EMBEDDING_LOCATION = os.getenv("EMBEDDING_LOCATION", "asia-south1")

sa_raw = os.getenv("VERTEX_AI_SA_API")
if not sa_raw:
    raise RuntimeError("Missing VERTEX_AI_SA_API in environment. Please provide your service account JSON.")
try:
    sa_info = json.loads(sa_raw)
except json.JSONDecodeError as e:
    raise ValueError(f"Invalid JSON in VERTEX_AI_SA_API environment variable: {e}")
creds = Credentials.from_service_account_info(sa_info, scopes=["https://www.googleapis.com/auth/cloud-platform"])

def initialize_gen_client() -> genai.Client:
    if not GCP_PROJECT_ID:
        raise ValueError("GCP_PROJECT_ID is not set. Cannot initialize GenAI client.")
    return genai.Client(vertexai=True, project=GCP_PROJECT_ID, location=GCP_LOCATION, credentials=creds)

def initialize_emb_client() -> genai.Client:
    if not GCP_PROJECT_ID:
        raise ValueError("GCP_PROJECT_ID is not set. Cannot initialize Embedding client.")
    return genai.Client(vertexai=True, project=GCP_PROJECT_ID, location=EMBEDDING_LOCATION, credentials=creds)

initialize_client = initialize_gen_client

vertexai.init(project=GCP_PROJECT_ID, location=EMBEDDING_LOCATION, credentials=creds)
_EMBED_MODEL = TextEmbeddingModel.from_pretrained(EMBEDDING_MODEL_ID)

class GeminiEmbeddingFunction(EmbeddingFunction):
    def __init__(self, model_id: str = EMBEDDING_MODEL_ID):
        self.model_id = model_id
        self.model = _EMBED_MODEL
        self.dim = len(self.model.get_embeddings(["a"])[0].values)

    def __call__(self, inputs: Documents) -> Embeddings:
        if not isinstance(inputs, list) or not all(isinstance(i, str) for i in inputs):
            raise TypeError("Inputs for embedding must be a list of strings.")
        valid_texts = []
        mask = []
        for doc in inputs:
            if isinstance(doc, str) and doc.strip():
                valid_texts.append(doc)
                mask.append(True)
            else:
                mask.append(False)
        zero_vector = [0.0] * self.dim
        result = []
        if valid_texts:
            emb_values = []
            for text in valid_texts:
                emb = self.model.get_embeddings([text])[0].values
                emb_values.append(emb)
            idx = 0
            for keep in mask:
                if keep:
                    result.append(emb_values[idx])
                    idx += 1
                else:
                    result.append(zero_vector)
        else:
            for _ in inputs:
                result.append(zero_vector)
        return result

def generate_stream(user_messages: List[str], model: str = MODEL_ID, temperature: float = TEMPERATURE, top_p: float = TOP_P, max_output_tokens: int = MAX_CACHE_SIZE) -> Generator[str, None, None]:
    client = initialize_gen_client()
    contents = [types.Part.from_text(msg) for msg in user_messages] # type: ignore
    cfg = types.GenerateContentConfig(
        temperature=temperature,
        top_p=top_p,
        max_output_tokens=max_output_tokens,
        safety_settings=[
            types.SafetySetting(category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold=types.HarmBlockThreshold.OFF),
            types.SafetySetting(category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold=types.HarmBlockThreshold.OFF),
            types.SafetySetting(category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold=types.HarmBlockThreshold.OFF),
            types.SafetySetting(category=types.HarmCategory.HARM_CATEGORY_HARASSMENT, threshold=types.HarmBlockThreshold.OFF),
        ],
    )
    for chunk in client.models.generate_content_stream(model=model, contents=contents, config=cfg):
        yield chunk.text # type: ignore
