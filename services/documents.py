from typing import List
from pydantic import BaseModel
from pypdf import Pdf<PERSON>eader
from io import BytesIO
import requests
from docx import Document as DocxDocument
from utils.logger import logger
import os
from urllib.parse import urlparse  # added for robust file-type parsing
from config.settings import settings

# Log the current environment configuration for debugging
logger.info(f"Documents service initialized with BASE_URL: {settings.base_url}")

class Document(BaseModel):
    organisation_id: str
    role: str
    documents: List[str]

class Documents():
    def __init__(self):
        pass
    
    def get_documents(self):
        url = f"{settings.base_url}/policy/all"
        response = requests.get(url, headers={
            "accept": "application/json, text/plain, */*",
            "accept-language": settings.accept_language,
            "X-Api-Key": settings.api_key,
        })
        
        return response.json().get('response', [])

    def download_document(self, document_url: str) -> bytes | None:
        try:
            response = requests.get(document_url)
            if (response.status_code != 200):
                logger.warning(f"Failed to download document from {document_url}. Status code: {response.status_code}")
                return None
            return response.content
        except requests.exceptions.RequestException as e:
            logger.error(f"Error downloading document from {document_url}: {e}")
            return None
        except Exception as e:
            logger.error(f"An unexpected error occurred while downloading document from {document_url}: {e}")
            return None

    def generate_embeddings(self, file_content: bytes, file_type: str | None) -> str:
        text = ""
        if file_type == 'pdf':
            pdf_data = BytesIO(file_content)
            reader = PdfReader(pdf_data)
            page_texts = []
            for page in reader.pages:
                extracted_text = page.extract_text()
                if extracted_text:
                    page_texts.append(extracted_text)
            text = "\n".join(page_texts)
        elif file_type == 'docx':
            document = DocxDocument(BytesIO(file_content))
            
            full_text = []
            for para in document.paragraphs:
                full_text.append(para.text)
                
            for table in document.tables:
                for row in table.rows:
                    for cell in row.cells:
                        full_text.append(cell.text) 
            text = "\n".join(full_text)
        else:
            logger.warning(f"Attempted to generate embeddings for unsupported file type: {file_type}")
            raise ValueError("Unsupported file type for embedding generation")
        
        return text

    def _get_file_type(self, document: str) -> str | None:
        """
        Determine file type from the document URL/path, stripping off any query params.
        Returns 'pdf' or 'docx' (or None if unsupported).
        """

        path = urlparse(document).path
        _, ext = os.path.splitext(path)
        ext = ext.lower()
        if ext == ".pdf":
            return "pdf"
        if ext in (".docx", ".doc"):
            return "docx"
        return None
