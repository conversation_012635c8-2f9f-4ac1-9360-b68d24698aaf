"""
Application settings using Pydantic BaseSettings for environment variable management.
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


def getEnvFileFromNodeEnv():
    node_env = os.getenv("NODE_ENV", "development")
    if node_env == "production":
        return ".env.production"
    elif node_env == "uat":
        return ".env.uat"
    else:
        return ".env.development"


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=getEnvFileFromNodeEnv(), env_file_encoding="utf-8"
    )


effi_global_settings = Settings()

print(f"Using environment file: {getEnvFileFromNodeEnv()}, settings: {effi_global_settings.model_dump()}")